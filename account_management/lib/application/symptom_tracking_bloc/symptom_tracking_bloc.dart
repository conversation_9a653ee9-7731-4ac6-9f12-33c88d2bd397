import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';

import '../../domain/facade/period_tracking_facade.dart';
import '../../domain/failure/period_tracking_failure.dart';
import '../../domain/model/symptom_model.dart';

part 'symptom_tracking_event.dart';
part 'symptom_tracking_state.dart';
part 'symptom_tracking_bloc.freezed.dart';

@injectable
class SymptomTrackingBloc extends Bloc<SymptomTrackingEvent, SymptomTrackingState> {
  final PeriodTrackingFacade _periodTrackingFacade;

  SymptomTrackingBloc(this._periodTrackingFacade) : super(const SymptomTrackingState.initial()) {
    on<SymptomTrackingEvent>((event, emit) async {
      await event.when(
        saveSymptomData: (date, symptoms, painLevel, flowLevel) => 
            _onSaveSymptomData(date, symptoms, painLevel, flowLevel, emit),
      );
    });
  }

  Future<void> _onSaveSymptomData(
    DateTime date,
    List<SymptomModel>? symptoms,
    int? painLevel,
    int? flowLevel,
    Emitter<SymptomTrackingState> emit,
  ) async {
    emit(const SymptomTrackingState.loading());
    
    final result = await _periodTrackingFacade.saveSymptomData(
      date: date,
      symptoms: symptoms,
      painLevel: painLevel,
      flowLevel: flowLevel,
    );
    
    result.mapBoth(
      onLeft: (failure) => emit(SymptomTrackingState.failure(failure)),
      onRight: (_) => emit(const SymptomTrackingState.success()),
    );
  }
}
