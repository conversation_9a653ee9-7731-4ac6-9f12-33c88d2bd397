import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import '../domain/facade/period_tracking_facade.dart';
import '../domain/failure/period_tracking_failure.dart';
import '../domain/model/period_tracking_model.dart';
import '../domain/model/symptom_model.dart';

@LazySingleton(as: PeriodTrackingFacade)
class PeriodTrackingRepository implements PeriodTrackingFacade {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  @override
  Stream<
      Either<PeriodTrackingFailure,
          Map<String, Map<String, PeriodTrackingModel>>>> watchYearData(
      int year) {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return Stream.value(
            const Left(PeriodTrackingFailure.unauthenticated()));
      }

      // Watch all months for the given year
      return _firestore
          .collection('period_tracking')
          .doc(user.uid)
          .collection('years')
          .doc(year.toString())
          .collection('months')
          .snapshots()
          .map<
              Either<PeriodTrackingFailure,
                  Map<String, Map<String, PeriodTrackingModel>>>>((snapshot) {
        try {
          final yearData = <String, Map<String, PeriodTrackingModel>>{};

          for (final doc in snapshot.docs) {
            final monthKey = doc.id; // e.g., "2025_01"
            final monthData = doc.data();
            final daysData = monthData['days'] as Map<String, dynamic>? ?? {};

            final monthPeriodData = <String, PeriodTrackingModel>{};

            for (final dayEntry in daysData.entries) {
              final dayKey = dayEntry.key; // e.g., "01"
              final dayData = dayEntry.value as Map<String, dynamic>;

              // Convert day data to PeriodTrackingModel
              final periodModel = PeriodTrackingModel.fromJson(dayData);

              monthPeriodData[dayKey] = periodModel;
            }

            if (monthPeriodData.isNotEmpty) {
              yearData[monthKey] = monthPeriodData;
            }
          }

          return Right(yearData);
        } catch (e) {
          return const Left(PeriodTrackingFailure.unexpected());
        }
      }).handleError((error) {
        return const Left(PeriodTrackingFailure.getPeriodTrackingsFailure());
      });
    } catch (e) {
      return Stream.value(const Left(PeriodTrackingFailure.unexpected()));
    }
  }

  @override
  Future<Either<PeriodTrackingFailure, Unit>> selectPeriodDates(
      Set<DateTime> selectedDates) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return const Left(PeriodTrackingFailure.unauthenticated());
      }

      // Use batch writes for efficiency
      final batch = _firestore.batch();

      // Group dates by month for efficient updates
      final Map<String, Map<String, Set<String>>> monthlyUpdates = {};

      for (final date in selectedDates) {
        final year = date.year.toString();
        final monthKey =
            '${date.year}_${date.month.toString().padLeft(2, '0')}';
        final dayKey = date.day.toString().padLeft(2, '0');

        monthlyUpdates[year] ??= {};
        monthlyUpdates[year]![monthKey] ??= {};
        monthlyUpdates[year]![monthKey]!.add(dayKey);
      }

      // Update each month document
      for (final yearEntry in monthlyUpdates.entries) {
        final year = yearEntry.key;

        for (final monthEntry in yearEntry.value.entries) {
          final monthKey = monthEntry.key;
          final dayKeys = monthEntry.value;

          final monthDocRef = _firestore
              .collection('period_tracking')
              .doc(user.uid)
              .collection('years')
              .doc(year)
              .collection('months')
              .doc(monthKey);

          // First, read the existing document to preserve data
          final existingDoc = await monthDocRef.get();
          final existingData = existingDoc.exists
              ? existingDoc.data() as Map<String, dynamic>?
              : null;
          final existingDays =
              existingData?['days'] as Map<String, dynamic>? ?? {};

          // Create the complete days structure
          final updatedDays = Map<String, dynamic>.from(existingDays);

          for (final dayKey in dayKeys) {
            final dayNum = int.parse(dayKey);
            final monthParts = monthKey.split('_');
            final year = int.parse(monthParts[0]);
            final month = int.parse(monthParts[1]);

            // Get existing day data or create new
            final existingDayData =
                updatedDays[dayKey] as Map<String, dynamic>? ?? {};

            // Update only the necessary fields, preserving others
            updatedDays[dayKey] = {
              ...existingDayData,
              'isPeriodDate': true,
              'lastUpdated': FieldValue.serverTimestamp(),
              'date': existingDayData['date'] ??
                  Timestamp.fromDate(DateTime(year, month, dayNum)),
            };
          }

          // update the complete document structure
          batch.update(
            monthDocRef,
            {'days': updatedDays},
          );
        }
      }

      // Commit the batch
      await batch.commit();

      return const Right(unit);
    } catch (e) {
      return const Left(PeriodTrackingFailure.createFailure());
    }
  }

  @override
  Future<Either<PeriodTrackingFailure, Unit>> deselectPeriodDates(
      Set<DateTime> datesToDeselect) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return const Left(PeriodTrackingFailure.unauthenticated());
      }

      // Use batch writes for efficiency
      final batch = _firestore.batch();

      // Group dates by month for efficient updates
      final Map<String, Map<String, Set<String>>> monthlyUpdates = {};

      for (final date in datesToDeselect) {
        final year = date.year.toString();
        final monthKey =
            '${date.year}_${date.month.toString().padLeft(2, '0')}';
        final dayKey = date.day.toString().padLeft(2, '0');

        monthlyUpdates[year] ??= {};
        monthlyUpdates[year]![monthKey] ??= {};
        monthlyUpdates[year]![monthKey]!.add(dayKey);
      }

      // Update each month document
      for (final yearEntry in monthlyUpdates.entries) {
        final year = yearEntry.key;

        for (final monthEntry in yearEntry.value.entries) {
          final monthKey = monthEntry.key;
          final dayKeys = monthEntry.value;

          final monthDocRef = _firestore
              .collection('period_tracking')
              .doc(user.uid)
              .collection('years')
              .doc(year)
              .collection('months')
              .doc(monthKey);

          // Create update map for this month
          final Map<String, dynamic> updates = {};
          for (final dayKey in dayKeys) {
            // Set isPeriodDate to false, preserving other data
            updates['days.$dayKey.isPeriodDate'] = false;
            updates['days.$dayKey.lastUpdated'] = FieldValue.serverTimestamp();
          }

          // Use set with merge to handle both new and existing documents
          batch.update(
            monthDocRef,
            updates,
          );
        }
      }

      // Commit the batch
      await batch.commit();

      return const Right(unit);
    } catch (e) {
      return const Left(PeriodTrackingFailure.createFailure());
    }
  }

  @override
  Future<Either<PeriodTrackingFailure, Unit>> calculateAndSaveOvulationDates(
      Set<DateTime> periodDates) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return const Left(PeriodTrackingFailure.unauthenticated());
      }

      // Calculate potential ovulation range for the period dates (more targeted approach)
      final potentialOvulationRange =
          _calculatePotentialOvulationRange(periodDates);

      // Get existing ovulation dates only in the calculated range
      final existingOvulationDates = await _getExistingOvulationDatesInRange(
          potentialOvulationRange.start, potentialOvulationRange.end);

      // Calculate new ovulation dates for valid period cycles
      final newOvulationDates = _calculateOvulationDates(periodDates);

      // Use batch writes for efficiency
      final batch = _firestore.batch();

      // Step 1: Clear existing ovulation dates (set to false)
      if (existingOvulationDates.isNotEmpty) {
        await _clearOvulationDatesInBatch(batch, existingOvulationDates);
      }

      // Step 2: Set new ovulation dates (set to true)
      if (newOvulationDates.isNotEmpty) {
        await _setOvulationDatesInBatch(batch, newOvulationDates);
      }

      await batch.commit();
      return const Right(unit);
    } catch (e) {
      return const Left(PeriodTrackingFailure.createFailure());
    }
  }

  @override
  Future<Either<PeriodTrackingFailure, Unit>> removeOvulationDatesForCycles(
      Set<DateTime> periodDatesToRemove) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return const Left(PeriodTrackingFailure.unauthenticated());
      }

      // Calculate the potential ovulation range for the deselected period dates
      final potentialOvulationRange =
          _calculatePotentialOvulationRange(periodDatesToRemove);

      // Get existing ovulation dates in that range
      final ovulationDatesToRemove = await _getExistingOvulationDatesInRange(
          potentialOvulationRange.start, potentialOvulationRange.end);

      if (ovulationDatesToRemove.isEmpty) {
        return const Right(unit); // No ovulation dates to remove
      }

      // Group ovulation dates by month for efficient updates
      final Map<String, Map<String, Set<String>>> monthlyUpdates = {};

      for (final date in ovulationDatesToRemove) {
        final year = date.year.toString();
        final monthKey =
            '${date.year}_${date.month.toString().padLeft(2, '0')}';
        final dayKey = date.day.toString().padLeft(2, '0');

        monthlyUpdates[year] ??= {};
        monthlyUpdates[year]![monthKey] ??= {};
        monthlyUpdates[year]![monthKey]!.add(dayKey);
      }

      // Use batch writes for efficiency
      final batch = _firestore.batch();

      // Update each month document
      for (final yearEntry in monthlyUpdates.entries) {
        final year = yearEntry.key;

        for (final monthEntry in yearEntry.value.entries) {
          final monthKey = monthEntry.key;
          final dayKeys = monthEntry.value;

          final monthDocRef = _firestore
              .collection('period_tracking')
              .doc(user.uid)
              .collection('years')
              .doc(year)
              .collection('months')
              .doc(monthKey);

          // Create update map for ovulation dates
          final Map<String, dynamic> updates = {};
          for (final dayKey in dayKeys) {
            updates['days.$dayKey.isOvulationDate'] = false;
            updates['days.$dayKey.lastUpdated'] = FieldValue.serverTimestamp();
          }

          batch.update(monthDocRef, updates);
        }
      }

      await batch.commit();
      return const Right(unit);
    } catch (e) {
      return const Left(PeriodTrackingFailure.createFailure());
    }
  }

  // Calculate ovulation dates for valid period cycles
  Set<DateTime> _calculateOvulationDates(Set<DateTime> periodDates) {
    if (periodDates.isEmpty) return {};

    final ovulationDates = <DateTime>{};
    final periodCycles = _calculatePeriodCycles(periodDates);

    for (int i = 0; i < periodCycles.length; i++) {
      final currentCycle = periodCycles[i];

      // Conditions for valid ovulation calculation:
      // 1. Cycle must have at least 3 consecutive days
      // 2. If there's a next cycle, gap should be reasonable (21-35 days)
      if (!_isValidCycleForOvulation(currentCycle, periodCycles, i)) {
        continue;
      }

      final cycleStartDate = currentCycle.first;
      DateTime? nextCycleStartDate;

      // Find next cycle start date
      if (i + 1 < periodCycles.length) {
        nextCycleStartDate = periodCycles[i + 1].first;
      } else {
        // For the last cycle, estimate based on average cycle length (28 days)
        nextCycleStartDate = cycleStartDate.add(const Duration(days: 28));
      }

      // Calculate ovulation window (5 days: 2 days before peak + peak + 2 days after)
      final ovulationPeakDate =
          nextCycleStartDate.subtract(const Duration(days: 14));

      // Only add if ovulation peak date is reasonable (not in the past cycle)
      if (ovulationPeakDate
          .isAfter(cycleStartDate.add(const Duration(days: 7)))) {
        // Create 5-day ovulation window: 2 days before peak, peak day, 2 days after peak
        for (int j = -2; j <= 2; j++) {
          final ovulationDate = ovulationPeakDate.add(Duration(days: j));

          // Ensure ovulation date is not in the period cycle itself
          if (ovulationDate
              .isAfter(cycleStartDate.add(const Duration(days: 7)))) {
            ovulationDates.add(ovulationDate);
          }
        }
      }
    }

    return ovulationDates;
  }

  // Calculate potential ovulation date range (max 60 days from period dates)
  ({DateTime start, DateTime end}) _calculatePotentialOvulationRange(
      Set<DateTime> periodDates) {
    if (periodDates.isEmpty) {
      final now = DateTime.now();
      return (start: now, end: now);
    }

    final earliestPeriod = periodDates.reduce((a, b) => a.isBefore(b) ? a : b);
    final latestPeriod = periodDates.reduce((a, b) => a.isAfter(b) ? a : b);

    // Ovulation can occur 7-35 days after period start
    final rangeStart = earliestPeriod.add(const Duration(days: 7));
    final rangeEnd = latestPeriod.add(const Duration(days: 35));

    return (start: rangeStart, end: rangeEnd);
  }

  // Get existing ovulation dates within a specific date range
  Future<Set<DateTime>> _getExistingOvulationDatesInRange(
      DateTime startDate, DateTime endDate) async {
    final user = _auth.currentUser;
    if (user == null) return {};

    try {
      final ovulationDates = <DateTime>{};

      // Get all years that might contain ovulation dates in the range
      final startYear = startDate.year;
      final endYear = endDate.year;

      for (int year = startYear; year <= endYear; year++) {
        final snapshot = await _firestore
            .collection('period_tracking')
            .doc(user.uid)
            .collection('years')
            .doc(year.toString())
            .collection('months')
            .get();

        for (final doc in snapshot.docs) {
          final monthData = doc.data();
          final daysData = monthData['days'] as Map<String, dynamic>? ?? {};

          for (final dayEntry in daysData.entries) {
            final dayData = dayEntry.value as Map<String, dynamic>;
            if (dayData['isOvulationDate'] == true) {
              final date = dayData['date'] as Timestamp?;
              if (date != null) {
                final ovulationDate = date.toDate();
                // Only include dates within the specified range
                if (ovulationDate
                        .isAfter(startDate.subtract(const Duration(days: 1))) &&
                    ovulationDate
                        .isBefore(endDate.add(const Duration(days: 1)))) {
                  ovulationDates.add(ovulationDate);
                }
              }
            }
          }
        }
      }

      return ovulationDates;
    } catch (e) {
      return {};
    }
  }

  // Clear existing ovulation dates in batch
  Future<void> _clearOvulationDatesInBatch(
      WriteBatch batch, Set<DateTime> ovulationDates) async {
    final monthlyUpdates = <String, Map<String, Set<String>>>{};

    for (final date in ovulationDates) {
      final year = date.year.toString();
      final monthKey = '${date.year}_${date.month.toString().padLeft(2, '0')}';
      final dayKey = date.day.toString().padLeft(2, '0');

      monthlyUpdates[year] ??= {};
      monthlyUpdates[year]![monthKey] ??= {};
      monthlyUpdates[year]![monthKey]!.add(dayKey);
    }

    final user = _auth.currentUser;
    if (user == null) return;

    for (final yearEntry in monthlyUpdates.entries) {
      final year = yearEntry.key;

      for (final monthEntry in yearEntry.value.entries) {
        final monthKey = monthEntry.key;
        final dayKeys = monthEntry.value;

        final monthDocRef = _firestore
            .collection('period_tracking')
            .doc(user.uid)
            .collection('years')
            .doc(year)
            .collection('months')
            .doc(monthKey);

        final Map<String, dynamic> updates = {};
        for (final dayKey in dayKeys) {
          updates['days.$dayKey.isOvulationDate'] = false;
          updates['days.$dayKey.lastUpdated'] = FieldValue.serverTimestamp();
        }

        batch.update(monthDocRef, updates);
      }
    }
  }

  // Set new ovulation dates in batch
  Future<void> _setOvulationDatesInBatch(
      WriteBatch batch, Set<DateTime> ovulationDates) async {
    final monthlyUpdates = <String, Map<String, Set<String>>>{};

    for (final date in ovulationDates) {
      final year = date.year.toString();
      final monthKey = '${date.year}_${date.month.toString().padLeft(2, '0')}';
      final dayKey = date.day.toString().padLeft(2, '0');

      monthlyUpdates[year] ??= {};
      monthlyUpdates[year]![monthKey] ??= {};
      monthlyUpdates[year]![monthKey]!.add(dayKey);
    }

    final user = _auth.currentUser;
    if (user == null) return;

    for (final yearEntry in monthlyUpdates.entries) {
      final year = yearEntry.key;

      for (final monthEntry in yearEntry.value.entries) {
        final monthKey = monthEntry.key;
        final dayKeys = monthEntry.value;

        final monthDocRef = _firestore
            .collection('period_tracking')
            .doc(user.uid)
            .collection('years')
            .doc(year)
            .collection('months')
            .doc(monthKey);

        final Map<String, dynamic> updates = {};
        for (final dayKey in dayKeys) {
          final dayNum = int.parse(dayKey);
          final monthParts = monthKey.split('_');
          final yearNum = int.parse(monthParts[0]);
          final monthNum = int.parse(monthParts[1]);

          updates['days.$dayKey.isOvulationDate'] = true;
          updates['days.$dayKey.lastUpdated'] = FieldValue.serverTimestamp();
          updates['days.$dayKey.date'] =
              Timestamp.fromDate(DateTime(yearNum, monthNum, dayNum));
        }

        batch.update(monthDocRef, updates);
      }
    }
  }

  // Calculate period cycles from period dates
  List<List<DateTime>> _calculatePeriodCycles(Set<DateTime> periodDates) {
    if (periodDates.isEmpty) return [];

    final sortedDates = periodDates.toList()..sort();
    List<List<DateTime>> cycles = [];
    List<DateTime> currentCycle = [];

    for (int i = 0; i < sortedDates.length; i++) {
      final currentDate = sortedDates[i];

      if (currentCycle.isEmpty) {
        currentCycle.add(currentDate);
      } else {
        final lastDate = currentCycle.last;
        final daysDifference = currentDate.difference(lastDate).inDays;

        if (daysDifference <= 1) {
          // Consecutive day - add to current cycle
          currentCycle.add(currentDate);
        } else {
          // Gap found - end current cycle and start new one
          cycles.add(List.from(currentCycle));
          currentCycle = [currentDate];
        }
      }
    }

    // Add the last cycle
    if (currentCycle.isNotEmpty) {
      cycles.add(currentCycle);
    }

    return cycles;
  }

  // Check if a period cycle is valid for ovulation calculation
  bool _isValidCycleForOvulation(List<DateTime> currentCycle,
      List<List<DateTime>> allCycles, int cycleIndex) {
    // Condition 1: Cycle must have at least 1 day
    if (currentCycle.isEmpty) {
      return false;
    }

    // Condition 2: If there's a next cycle, check the gap is reasonable
    if (cycleIndex + 1 < allCycles.length) {
      final nextCycle = allCycles[cycleIndex + 1];
      final daysBetweenCycles =
          nextCycle.first.difference(currentCycle.last).inDays;

      // Gap should be between 14-35 days for regular cycles
      if (daysBetweenCycles < 14 || daysBetweenCycles > 35) {
        return false;
      }
    }

    // Condition 3: For irregular periods, ensure cycle isn't too short
    final cycleDuration =
        currentCycle.last.difference(currentCycle.first).inDays + 1;

    if (cycleDuration > 10) {
      // If period lasts more than 10 days, it might be irregular data
      return false;
    }

    return true;
  }

  @override
  Future<Either<PeriodTrackingFailure, Unit>> saveSymptomData({
    required DateTime date,
    List<SymptomModel>? symptoms,
    int? painLevel,
    int? flowLevel,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return const Left(PeriodTrackingFailure.unauthenticated());
      }

      final year = date.year.toString();
      final monthKey = '${date.year}_${date.month.toString().padLeft(2, '0')}';
      final dayKey = date.day.toString().padLeft(2, '0');

      final monthDocRef = _firestore
          .collection('period_tracking')
          .doc(user.uid)
          .collection('years')
          .doc(year)
          .collection('months')
          .doc(monthKey);

      // Prepare the update data
      final Map<String, dynamic> updates = {};

      if (symptoms != null) {
        updates['days.$dayKey.symptoms'] =
            symptoms.map((s) => s.toJson()).toList();
      }

      if (painLevel != null) {
        updates['days.$dayKey.painLevel'] = painLevel;
      }

      if (flowLevel != null) {
        updates['days.$dayKey.flowLevel'] = flowLevel;
      }

      // Always update the timestamp and date
      updates['days.$dayKey.lastUpdated'] = FieldValue.serverTimestamp();
      updates['days.$dayKey.date'] = Timestamp.fromDate(date);

      // Use set with merge to handle both new and existing documents
      await monthDocRef.update({
        'days': {
          dayKey: updates.map((key, value) => MapEntry(key.split('.').last, value))
        }
      }, );

      return const Right(unit);
    } catch (e) {
      return const Left(PeriodTrackingFailure.createFailure());
    }
  }
}
