import 'package:fpdart/fpdart.dart';
import '../failure/period_tracking_failure.dart';
import '../model/period_tracking_model.dart';
import '../model/symptom_model.dart';

abstract class PeriodTrackingFacade {
  /// Watches period tracking data for a specific year.
  /// Returns a stream of Either<PeriodTrackingFailure, Map<String, Map<String, PeriodTrackingModel>>>
  /// where the outer map key is month (e.g., "2025_01") and inner map key is day (e.g., "01")
  Stream<
      Either<PeriodTrackingFailure,
          Map<String, Map<String, PeriodTrackingModel>>>> watchYearData(
      int year);

  /// Selects period dates in Firestore (sets isPeriodDate: true)
  Future<Either<PeriodTrackingFailure, Unit>> selectPeriodDates(
      Set<DateTime> selectedDates);

  /// Deselects period dates in Firestore (sets isPeriodDate: false)
  Future<Either<PeriodTrackingFailure, Unit>> deselectPeriodDates(
      Set<DateTime> datesToDeselect);

  /// Calculates and saves ovulation dates based on period cycles
  Future<Either<PeriodTrackingFailure, Unit>> calculateAndSaveOvulationDates(
      Set<DateTime> periodDates);

  /// Removes ovulation dates for specific period cycles
  Future<Either<PeriodTrackingFailure, Unit>> removeOvulationDatesForCycles(
      Set<DateTime> periodDatesToRemove);

  /// Saves symptom data for a specific date
  Future<Either<PeriodTrackingFailure, Unit>> saveSymptomData({
    required DateTime date,
    List<SymptomModel>? symptoms,
    int? painLevel,
    int? flowLevel,
  });
}
