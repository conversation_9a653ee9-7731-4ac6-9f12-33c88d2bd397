import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:auto_route/auto_route.dart';
import 'package:intl/intl.dart';

import 'package:account_management/application/period_tracking_watcher_bloc/period_tracking_watcher_bloc.dart';
import 'package:account_management/application/symptom_tracking_bloc/symptom_tracking_bloc.dart';
import 'package:account_management/domain/model/symptom_model.dart';
import 'package:design_system/design/theme.dart';

import '../../custom_widgets/weekly_calendar.dart';
import '../../custom_widgets/menstural_cycle_dial.dart';
import '../../services/cycle_day_calculator.dart';

@RoutePage()
class DailySymptomTrackingPage extends StatefulWidget {
  final DateTime? initialDate;

  const DailySymptomTrackingPage({Key? key, this.initialDate})
      : super(key: key);

  @override
  State<DailySymptomTrackingPage> createState() =>
      _DailySymptomTrackingPageState();
}

class _DailySymptomTrackingPageState extends State<DailySymptomTrackingPage> {
  late DateTime _selectedDate;
  final ScrollController _scrollController = ScrollController();

  // Symptom tracking state
  List<SymptomModel> _selectedSymptoms = [];
  int _painLevel = 0;
  int _flowLevel = 0;

  final List<SymptomModel> _availableSymptoms = [
    SymptomModel(name: 'Headache'),
    SymptomModel(name: 'Fatigue'),
    SymptomModel(name: 'Bloating'),
    SymptomModel(name: 'Back Pain'),
    SymptomModel(name: 'Cramps'),
    SymptomModel(name: 'Breakouts'),
    SymptomModel(name: 'Mood Swings'),
    SymptomModel(name: 'Tender Breasts'),
  ];

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.initialDate ?? DateTime.now();

    // Watch period tracking data for the current year
    context.read<PeriodTrackingWatcherBloc>().add(
          PeriodTrackingWatcherEvent.watchYearStarted(_selectedDate.year),
        );
  }

  void _onDateSelected(DateTime date) {
    setState(() {
      _selectedDate = date;
      // Reset symptom data when date changes
      _selectedSymptoms.clear();
      _painLevel = 0;
      _flowLevel = 0;
    });
  }

  void _saveSymptomData() {
    context.read<SymptomTrackingBloc>().add(
          SymptomTrackingEvent.saveSymptomData(
            date: _selectedDate,
            symptoms: _selectedSymptoms.isNotEmpty ? _selectedSymptoms : null,
            painLevel: _painLevel > 0 ? _painLevel : null,
            flowLevel: _flowLevel > 0 ? _flowLevel : null,
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xffFBF0D5),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: AppTheme.primaryColor),
          onPressed: () => context.router.pop(),
        ),
        title: Text(
          'Daily Symptoms',
          style: GoogleFonts.mulish(
            fontSize: 20.sp,
            fontWeight: FontWeight.w600,
            color: AppTheme.primaryColor,
          ),
        ),
        centerTitle: true,
      ),
      body: BlocListener<SymptomTrackingBloc, SymptomTrackingState>(
        listener: (context, state) {
          state.when(
            initial: () {},
            loading: () {},
            success: () {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Symptoms saved successfully!'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            failure: (failure) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Failed to save symptoms'),
                  backgroundColor: Colors.red,
                ),
              );
            },
          );
        },
        child: SingleChildScrollView(
          controller: _scrollController,
          child: Column(
            children: [
              SizedBox(height: 16.h),

              // Weekly Calendar
              BlocBuilder<PeriodTrackingWatcherBloc,
                  PeriodTrackingWatcherState>(
                builder: (context, state) {
                  return state.when(
                    initial: () => Container(),
                    loading: () => Container(
                        height: 120.h,
                        child: Center(child: CircularProgressIndicator())),
                    loadSuccess: (yearData) {
                      // Extract period and ovulation dates
                      final periodDates = <DateTime>{};
                      final ovulationDates = <DateTime>{};

                      for (final monthData in yearData.values) {
                        for (final dayData in monthData.values) {
                          if (dayData.isPeriodDate == true &&
                              dayData.date != null) {
                            periodDates.add(dayData.date!);
                          }
                          if (dayData.isOvulationDate == true &&
                              dayData.date != null) {
                            ovulationDates.add(dayData.date!);
                          }
                        }
                      }

                      return WeeklyCalendar(
                        selectedDate: _selectedDate,
                        onDateSelected: _onDateSelected,
                        periodDates: periodDates,
                        ovulationDates: ovulationDates,
                      );
                    },
                    loadFailure: (failure) => Container(
                      height: 120.h,
                      child:
                          Center(child: Text('Failed to load calendar data')),
                    ),
                  );
                },
              ),

              SizedBox(height: 24.h),

              // Selected Date Info
              Container(
                margin: EdgeInsets.symmetric(horizontal: 24.w),
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    Text(
                      DateFormat('EEEE, MMMM d, yyyy').format(_selectedDate),
                      style: GoogleFonts.mulish(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.w600,
                        color: Color(0xff2D2D2D),
                      ),
                    ),
                    SizedBox(height: 16.h),

                    // Menstrual Cycle Dial
                    BlocBuilder<PeriodTrackingWatcherBloc,
                        PeriodTrackingWatcherState>(
                      builder: (context, state) {
                        return state.when(
                          initial: () => Container(),
                          loading: () => Container(
                            height: 200.h,
                            child: Center(child: CircularProgressIndicator()),
                          ),
                          loadSuccess: (yearData) {
                            final cycleInfo =
                                CycleDayCalculator.calculateCycleDay(
                                    _selectedDate, yearData);

                            if (cycleInfo == null) {
                              return Container(
                                height: 200.h,
                                child: Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(Icons.calendar_today,
                                          size: 48, color: Colors.grey),
                                      SizedBox(height: 8.h),
                                      Text(
                                        'No cycle data available',
                                        style: GoogleFonts.mulish(
                                          fontSize: 16.sp,
                                          color: Colors.grey,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            }

                            return Column(
                              children: [
                                // Enhanced menstrual cycle tracker using calculated data
                                MenstrualCycleTracker.fromCycleInfo(
                                  cycleInfo: cycleInfo,
                                  size: 200,
                                  textStyle: GoogleFonts.mulish(
                                    color: Colors.white,
                                    fontSize: 16.sp,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  outerCircleColor: Color(0xffD3CFC6),
                                  periodArcColor: AppTheme.primaryColor,
                                  ovulationArcColor: Color(0xffECA83D),
                                  innerCircleColor: AppTheme.primaryColor,
                                  pointerColor: Colors.white,
                                ),
                                SizedBox(height: 16.h),
                                Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 16.w, vertical: 8.h),
                                  decoration: BoxDecoration(
                                    color: AppTheme.primaryColor
                                        .withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Text(
                                    '${cycleInfo.cyclePhase} Phase',
                                    style: GoogleFonts.mulish(
                                      fontSize: 14.sp,
                                      fontWeight: FontWeight.w600,
                                      color: AppTheme.primaryColor,
                                    ),
                                  ),
                                ),
                              ],
                            );
                          },
                          loadFailure: (failure) => Container(
                            height: 200.h,
                            child: Center(
                                child: Text('Failed to load cycle data')),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),

              SizedBox(height: 24.h),

              // Add Symptoms Button
              Container(
                margin: EdgeInsets.symmetric(horizontal: 24.w),
                child: Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          _showSymptomsBottomSheet();
                        },
                        icon: Icon(Icons.add, color: Colors.white),
                        label: Text(
                          'Add Symptoms',
                          style: GoogleFonts.mulish(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          padding: EdgeInsets.symmetric(vertical: 16.h),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              SizedBox(height: 32.h),
            ],
          ),
        ),
      ),
    );
  }

  void _showSymptomsBottomSheet() {
    showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) => Container(
          height: MediaQuery.of(context).size.height * 0.8,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(32),
              topRight: Radius.circular(32),
            ),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                margin: EdgeInsets.only(top: 12.h),
                width: 40.w,
                height: 4.h,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: EdgeInsets.all(24.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Track Symptoms',
                      style: GoogleFonts.mulish(
                        fontSize: 20.sp,
                        fontWeight: FontWeight.w600,
                        color: Color(0xff2D2D2D),
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.pop(context);
                        _saveSymptomData();
                      },
                      child: Text(
                        'Save',
                        style: GoogleFonts.mulish(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.symmetric(horizontal: 24.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Pain Level Section
                      _buildSectionTitle('Pain Level'),
                      SizedBox(height: 16.h),
                      Column(
                        children: [
                          Text(
                            'Pain Level: $_painLevel/10',
                            style: GoogleFonts.mulish(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w500,
                              color: Color(0xff666666),
                            ),
                          ),
                          SizedBox(height: 8.h),
                          Slider(
                            value: _painLevel.toDouble(),
                            min: 0,
                            max: 10,
                            divisions: 10,
                            activeColor: AppTheme.primaryColor,
                            inactiveColor: Colors.grey[300],
                            onChanged: (value) {
                              setModalState(() {
                                _painLevel = value.round();
                              });
                              setState(() {
                                _painLevel = value.round();
                              });
                            },
                          ),
                        ],
                      ),

                      SizedBox(height: 32.h),

                      // Flow Level Section
                      _buildSectionTitle('Flow Level'),
                      SizedBox(height: 16.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildFlowOption('None', 0, setModalState),
                          _buildFlowOption('Light', 1, setModalState),
                          _buildFlowOption('Medium', 2, setModalState),
                          _buildFlowOption('Heavy', 3, setModalState),
                        ],
                      ),

                      SizedBox(height: 32.h),

                      // Symptoms Section
                      _buildSectionTitle('Symptoms'),
                      SizedBox(height: 16.h),
                      Wrap(
                        spacing: 12.w,
                        runSpacing: 12.h,
                        children: _availableSymptoms.map((symptom) {
                          final isSelected =
                              _selectedSymptoms.contains(symptom);
                          return GestureDetector(
                            onTap: () {
                              setModalState(() {
                                if (isSelected) {
                                  _selectedSymptoms.remove(symptom);
                                } else {
                                  _selectedSymptoms.add(symptom);
                                }
                              });
                              setState(() {
                                if (isSelected) {
                                  _selectedSymptoms.remove(symptom);
                                } else {
                                  _selectedSymptoms.add(symptom);
                                }
                              });
                            },
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 16.w, vertical: 8.h),
                              decoration: BoxDecoration(
                                color: isSelected
                                    ? AppTheme.primaryColor
                                    : Colors.grey[100],
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(
                                  color: isSelected
                                      ? AppTheme.primaryColor
                                      : Colors.grey[300]!,
                                ),
                              ),
                              child: Text(
                                symptom.name,
                                style: GoogleFonts.mulish(
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w500,
                                  color: isSelected
                                      ? Colors.white
                                      : Color(0xff666666),
                                ),
                              ),
                            ),
                          );
                        }).toList(),
                      ),

                      SizedBox(height: 32.h),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: GoogleFonts.mulish(
        fontSize: 18.sp,
        fontWeight: FontWeight.w600,
        color: Color(0xff2D2D2D),
      ),
    );
  }

  Widget _buildFlowOption(String label, int value, StateSetter setModalState) {
    final isSelected = _flowLevel == value;
    return GestureDetector(
      onTap: () {
        setModalState(() {
          _flowLevel = value;
        });
        setState(() {
          _flowLevel = value;
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.primaryColor : Colors.grey[100],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? AppTheme.primaryColor : Colors.grey[300]!,
          ),
        ),
        child: Text(
          label,
          style: GoogleFonts.mulish(
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
            color: isSelected ? Colors.white : Color(0xff666666),
          ),
        ),
      ),
    );
  }
}
